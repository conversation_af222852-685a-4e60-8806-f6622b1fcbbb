<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心形测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b9d, #ffa726);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        #heart-canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            margin: 20px 0;
        }
        
        h1 {
            color: white;
            margin-bottom: 20px;
        }
        
        .status {
            color: white;
            margin-top: 20px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        
        button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>心形渲染测试</h1>
        <canvas id="heart-canvas" width="300" height="300"></canvas>
        <div>
            <button onclick="drawSimpleHeart()">绘制简单心形</button>
            <button onclick="clearCanvas()">清除画布</button>
        </div>
        <div class="status" id="status">等待绘制...</div>
    </div>

    <script>
        const canvas = document.getElementById('heart-canvas');
        const ctx = canvas.getContext('2d');
        const status = document.getElementById('status');

        function updateStatus(message) {
            status.textContent = message;
            console.log(message);
        }

        function drawSimpleHeart() {
            try {
                updateStatus('开始绘制心形...');
                
                // 清除画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 设置心形参数
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const scale = 2;
                
                // 开始绘制心形路径
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - 40 * scale);
                
                // 左侧曲线
                ctx.bezierCurveTo(
                    centerX - 50 * scale, centerY - 80 * scale,
                    centerX - 90 * scale, centerY - 40 * scale,
                    centerX - 50 * scale, centerY - 10 * scale
                );
                
                // 左下曲线
                ctx.bezierCurveTo(
                    centerX - 30 * scale, centerY + 10 * scale,
                    centerX - 20 * scale, centerY + 30 * scale,
                    centerX, centerY + 50 * scale
                );
                
                // 右下曲线
                ctx.bezierCurveTo(
                    centerX + 20 * scale, centerY + 30 * scale,
                    centerX + 30 * scale, centerY + 10 * scale,
                    centerX + 50 * scale, centerY - 10 * scale
                );
                
                // 右侧曲线
                ctx.bezierCurveTo(
                    centerX + 90 * scale, centerY - 40 * scale,
                    centerX + 50 * scale, centerY - 80 * scale,
                    centerX, centerY - 40 * scale
                );
                
                ctx.closePath();
                
                // 创建渐变
                const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                gradient.addColorStop(0, '#ff6b9d');
                gradient.addColorStop(0.5, '#ff8fab');
                gradient.addColorStop(1, '#ffb3c1');
                
                // 填充心形
                ctx.fillStyle = gradient;
                ctx.fill();
                
                // 添加发光效果
                ctx.shadowColor = '#ff6b9d';
                ctx.shadowBlur = 20;
                ctx.fill();
                
                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                
                // 添加高光
                ctx.beginPath();
                ctx.moveTo(centerX - 20 * scale, centerY - 30 * scale);
                ctx.bezierCurveTo(
                    centerX - 30 * scale, centerY - 50 * scale,
                    centerX - 10 * scale, centerY - 60 * scale,
                    centerX + 10 * scale, centerY - 30 * scale
                );
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.fill();
                
                updateStatus('心形绘制成功！');
                
            } catch (error) {
                updateStatus('绘制失败: ' + error.message);
                console.error('Heart drawing error:', error);
            }
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateStatus('画布已清除');
        }

        // 页面加载完成后自动绘制
        window.addEventListener('load', () => {
            updateStatus('页面加载完成，Canvas准备就绪');
            setTimeout(drawSimpleHeart, 500);
        });
    </script>
</body>
</html>
