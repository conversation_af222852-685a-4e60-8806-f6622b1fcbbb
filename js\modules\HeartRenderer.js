// 心形渲染模块
import { APP_CONFIG } from './config.js';

export class HeartRenderer {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.currentTheme = APP_CONFIG.THEMES.romantic;
        this.animationFrame = null;
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupHighDPI();
        this.draw();
    }
    
    setupCanvas() {
        const size = Math.min(
            window.innerWidth - 40, 
            APP_CONFIG.CANVAS.MAX_SIZE
        );
        
        this.canvas.width = size;
        this.canvas.height = size;
        this.canvas.style.width = size + 'px';
        this.canvas.style.height = size + 'px';
    }
    
    setupHighDPI() {
        if (!APP_CONFIG.CANVAS.DPR_ENABLED) return;
        
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    draw() {
        this.clear();
        this.drawHeart();
        this.addGlowEffect();
        this.addHighlight();
    }
    
    clear() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    drawHeart() {
        const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
        const centerY = this.canvas.height / (window.devicePixelRatio || 1) / 2;
        const scale = Math.min(this.canvas.width, this.canvas.height) / 
                     (window.devicePixelRatio || 1) / APP_CONFIG.CANVAS.HEART_SCALE_FACTOR;
        
        this.ctx.beginPath();
        this.ctx.moveTo(centerX, centerY - 40 * scale);
        
        // 左侧曲线
        this.ctx.bezierCurveTo(
            centerX - 50 * scale, centerY - 80 * scale,
            centerX - 90 * scale, centerY - 40 * scale,
            centerX - 50 * scale, centerY - 10 * scale
        );
        
        // 左下曲线
        this.ctx.bezierCurveTo(
            centerX - 30 * scale, centerY + 10 * scale,
            centerX - 20 * scale, centerY + 30 * scale,
            centerX, centerY + 50 * scale
        );
        
        // 右下曲线
        this.ctx.bezierCurveTo(
            centerX + 20 * scale, centerY + 30 * scale,
            centerX + 30 * scale, centerY + 10 * scale,
            centerX + 50 * scale, centerY - 10 * scale
        );
        
        // 右侧曲线
        this.ctx.bezierCurveTo(
            centerX + 90 * scale, centerY - 40 * scale,
            centerX + 50 * scale, centerY - 80 * scale,
            centerX, centerY - 40 * scale
        );
        
        this.ctx.closePath();
        
        // 创建渐变
        const gradient = this.createGradient();
        this.ctx.fillStyle = gradient;
        this.ctx.fill();
    }
    
    createGradient() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, this.currentTheme.primary);
        gradient.addColorStop(0.5, this.lightenColor(this.currentTheme.primary, 20));
        gradient.addColorStop(1, this.lightenColor(this.currentTheme.primary, 40));
        return gradient;
    }
    
    addGlowEffect() {
        this.ctx.shadowColor = this.currentTheme.primary;
        this.ctx.shadowBlur = 20;
        this.ctx.fill();
        
        // 重置阴影
        this.ctx.shadowColor = 'transparent';
        this.ctx.shadowBlur = 0;
    }
    
    addHighlight() {
        const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
        const centerY = this.canvas.height / (window.devicePixelRatio || 1) / 2;
        const scale = Math.min(this.canvas.width, this.canvas.height) / 
                     (window.devicePixelRatio || 1) / APP_CONFIG.CANVAS.HEART_SCALE_FACTOR;
        
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 20 * scale, centerY - 30 * scale);
        this.ctx.bezierCurveTo(
            centerX - 30 * scale, centerY - 50 * scale,
            centerX - 10 * scale, centerY - 60 * scale,
            centerX + 10 * scale, centerY - 30 * scale
        );
        
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.fill();
    }
    
    // 主题切换
    setTheme(themeName) {
        if (APP_CONFIG.THEMES[themeName]) {
            this.currentTheme = APP_CONFIG.THEMES[themeName];
            this.draw();
        }
    }
    
    // 颜色工具函数
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    // 动画效果
    animateExplosion() {
        let scale = 1;
        let opacity = 1;
        const animate = () => {
            this.clear();
            
            // 保存当前状态
            this.ctx.save();
            
            // 应用变换
            const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
            const centerY = this.canvas.height / (window.devicePixelRatio || 1) / 2;
            
            this.ctx.translate(centerX, centerY);
            this.ctx.scale(scale, scale);
            this.ctx.globalAlpha = opacity;
            this.ctx.translate(-centerX, -centerY);
            
            // 绘制心形
            this.drawHeart();
            this.addGlowEffect();
            this.addHighlight();
            
            // 恢复状态
            this.ctx.restore();
            
            // 更新动画参数
            scale += 0.02;
            opacity -= 0.02;
            
            if (scale < 1.3 && opacity > 0.7) {
                this.animationFrame = requestAnimationFrame(animate);
            } else {
                // 动画结束，恢复正常状态
                this.draw();
            }
        };
        
        animate();
    }
    
    // 脉冲动画
    animatePulse() {
        let scale = 1;
        let direction = 1;
        let frames = 0;
        const maxFrames = 30;
        
        const animate = () => {
            this.clear();
            
            // 保存当前状态
            this.ctx.save();
            
            // 应用变换
            const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
            const centerY = this.canvas.height / (window.devicePixelRatio || 1) / 2;
            
            this.ctx.translate(centerX, centerY);
            this.ctx.scale(scale, scale);
            this.ctx.translate(-centerX, -centerY);
            
            // 绘制心形
            this.drawHeart();
            this.addGlowEffect();
            this.addHighlight();
            
            // 恢复状态
            this.ctx.restore();
            
            // 更新动画参数
            scale += direction * 0.01;
            if (scale >= 1.1) direction = -1;
            if (scale <= 1) direction = 1;
            
            frames++;
            if (frames < maxFrames) {
                this.animationFrame = requestAnimationFrame(animate);
            } else {
                // 动画结束，恢复正常状态
                this.draw();
            }
        };
        
        animate();
    }
    
    // 停止动画
    stopAnimation() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
    }
    
    // 调整大小
    resize() {
        this.setupCanvas();
        this.setupHighDPI();
        this.draw();
    }
    
    // 获取画布信息
    getCanvasInfo() {
        return {
            width: this.canvas.width,
            height: this.canvas.height,
            centerX: this.canvas.width / (window.devicePixelRatio || 1) / 2,
            centerY: this.canvas.height / (window.devicePixelRatio || 1) / 2,
            scale: Math.min(this.canvas.width, this.canvas.height) / 
                   (window.devicePixelRatio || 1) / APP_CONFIG.CANVAS.HEART_SCALE_FACTOR
        };
    }
    
    // 销毁
    destroy() {
        this.stopAnimation();
        this.ctx = null;
        this.canvas = null;
    }
}
