// Heart Confession App - Main JavaScript (Refactored)
import { APP_CONFIG } from './modules/config.js';
import { HeartRenderer } from './modules/HeartRenderer.js';
import { AnimationManager } from './modules/AnimationManager.js';
import { AudioManager } from './modules/AudioManager.js';
import { StorageManager } from './modules/StorageManager.js';
import { UIManager } from './modules/UIManager.js';
import { PWAManager } from './modules/PWAManager.js';
import { PerformanceManager } from './modules/PerformanceManager.js';

class HeartConfessionApp {
    constructor() {
        this.currentMessage = 0;
        this.isAnimating = false;
        this.messages = [...APP_CONFIG.DEFAULT_MESSAGES];

        // 初始化管理器
        this.managers = {
            storage: new StorageManager(),
            ui: new UIManager(),
            performance: new PerformanceManager(),
            heart: new HeartRenderer('heart-canvas'),
            animation: new AnimationManager(),
            audio: new AudioManager('bg-music'),
            pwa: new PWAManager()
        };

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUserPreferences();
        this.setupCustomEventHandlers();
        this.managers.animation.createBackgroundParticles();
        this.managers.animation.animateBackgroundGradient();

        console.log('Heart Confession App initialized successfully');
        console.log('Performance report:', this.managers.performance.getPerformanceReport());
    }

    setupCustomEventHandlers() {
        // 监听性能优化事件
        window.addEventListener('performanceOptimized', (e) => {
            console.log('Performance optimized:', e.detail);
        });

        // 监听主题应用事件
        window.addEventListener('themeApplied', (e) => {
            this.managers.heart.setTheme(e.detail.theme);
        });

        // 监听画布调整事件
        window.addEventListener('canvasResize', () => {
            this.managers.heart.resize();
        });

        // 监听粒子更新事件
        window.addEventListener('particlesUpdate', () => {
            this.managers.animation.createBackgroundParticles();
        });

        // 监听音乐切换事件
        window.addEventListener('toggleMusic', () => {
            this.toggleMusic();
        });

        // 监听Toast显示事件
        window.addEventListener('showToast', (e) => {
            this.managers.ui.showToast(e.detail.message, e.detail.type);
        });
    }
    
    // 设置事件监听器（简化版）
    
    setupEventListeners() {
        const canvas = document.getElementById('heart-canvas');

        // Heart click event
        canvas?.addEventListener('click', (e) => this.handleHeartClick(e));

        // Music toggle
        document.getElementById('music-toggle')?.addEventListener('click', () => this.toggleMusic());

        // Share button
        document.getElementById('share-btn')?.addEventListener('click', () => this.shareApp());

        // Install button
        document.getElementById('install-btn')?.addEventListener('click', () => this.installApp());

        // Screenshot button
        document.getElementById('screenshot-btn')?.addEventListener('click', () => this.takeScreenshot());

        // Settings button
        document.getElementById('settings-btn')?.addEventListener('click', () => this.openSettings());

        // Modal controls
        document.getElementById('save-custom')?.addEventListener('click', () => this.saveCustomMessages());
        document.getElementById('cancel-custom')?.addEventListener('click', () => this.closeSettings());
        document.getElementById('reset-default')?.addEventListener('click', () => this.resetToDefault());

        // Close modal on backdrop click
        document.getElementById('settings-modal')?.addEventListener('click', (e) => {
            if (e.target.id === 'settings-modal') {
                this.closeSettings();
            }
        });

        // Prevent context menu on long press
        canvas?.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    drawHeart() {
        const ctx = this.ctx;
        const centerX = this.canvas.width / (window.devicePixelRatio || 1) / 2;
        const centerY = this.canvas.width / (window.devicePixelRatio || 1) / 2;
        const scale = Math.min(this.canvas.width, this.canvas.height) / (window.devicePixelRatio || 1) / 400;
        
        ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Create heart shape
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 40 * scale);
        
        // Left curve
        ctx.bezierCurveTo(
            centerX - 50 * scale, centerY - 80 * scale,
            centerX - 90 * scale, centerY - 40 * scale,
            centerX - 50 * scale, centerY - 10 * scale
        );
        
        // Left bottom curve
        ctx.bezierCurveTo(
            centerX - 30 * scale, centerY + 10 * scale,
            centerX - 20 * scale, centerY + 30 * scale,
            centerX, centerY + 50 * scale
        );
        
        // Right bottom curve
        ctx.bezierCurveTo(
            centerX + 20 * scale, centerY + 30 * scale,
            centerX + 30 * scale, centerY + 10 * scale,
            centerX + 50 * scale, centerY - 10 * scale
        );
        
        // Right curve
        ctx.bezierCurveTo(
            centerX + 90 * scale, centerY - 40 * scale,
            centerX + 50 * scale, centerY - 80 * scale,
            centerX, centerY - 40 * scale
        );
        
        ctx.closePath();
        
        // Create gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#ff6b9d');
        gradient.addColorStop(0.5, '#ff8fab');
        gradient.addColorStop(1, '#ffa8cc');
        
        ctx.fillStyle = gradient;
        ctx.fill();
        
        // Add glow effect
        ctx.shadowColor = '#ff6b9d';
        ctx.shadowBlur = 20;
        ctx.fill();
        
        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        
        // Add highlight
        ctx.beginPath();
        ctx.moveTo(centerX - 20 * scale, centerY - 30 * scale);
        ctx.bezierCurveTo(
            centerX - 30 * scale, centerY - 50 * scale,
            centerX - 10 * scale, centerY - 60 * scale,
            centerX + 10 * scale, centerY - 30 * scale
        );
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fill();
    }
    
    handleHeartClick(event) {
        if (this.isAnimating) return;
        
        // Try to enable music on first interaction if not already enabled
        if (!this.musicEnabled && !this.hasTriedAutoPlay) {
            this.hasTriedAutoPlay = true;
            this.tryAutoPlayMusic();
        }
        
        this.isAnimating = true;
        
        // Create click ripple effect
        this.createClickRipple(event);
        
        // Heart explosion animation
        this.animateHeartExplosion();
        
        // Progress to next message
        this.showNextMessage();
        
        // Create floating heart particles
        this.createHeartParticles();
        
        setTimeout(() => {
            this.isAnimating = false;
        }, 1000);
    }
    
    tryAutoPlayMusic() {
        const music = document.getElementById('bg-music');
        music.volume = 0.5;
        
        const playPromise = music.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                this.musicEnabled = true;
                this.updateMusicUI(true);
                localStorage.setItem('heartapp_music_enabled', true);
                console.log('Auto-play successful');
            }).catch(e => {
                console.log('Auto-play failed (normal):', e);
                // 添加用户交互解锁提示
                this.showUnlockButton();
            });
        }
    }
    
    showUnlockButton() {
        const musicToggle = document.getElementById('music-toggle');
        const originalHTML = musicToggle.innerHTML;
        
        // 创建解锁提示
        musicToggle.innerHTML = '<span id="music-icon">🔒</span><span id="music-status">点击解锁音乐</span>';
        musicToggle.classList.add('music-unlock');
        
        // 添加一次性点击事件
        const unlockHandler = () => {
            music.play().then(() => {
                console.log('User unlocked music');
                this.musicEnabled = true;
                this.updateMusicUI(true);
                localStorage.setItem('heartapp_music_enabled', true);
                musicToggle.classList.remove('music-unlock');
            }).catch(error => {
                console.error('User unlock failed', error);
                this.showToast('音乐播放失败，请检查音频文件');
            });
            
            // 恢复原始内容
            musicToggle.innerHTML = originalHTML;
            musicToggle.removeEventListener('click', unlockHandler);
        };
        
        musicToggle.addEventListener('click', unlockHandler);
    }
    
    updateMusicUI(isPlaying) {
        const musicIcon = document.getElementById('music-icon');
        const musicStatus = document.getElementById('music-status');
        const musicToggle = document.getElementById('music-toggle');
        
        if (isPlaying) {
            musicIcon.textContent = '🎶';
            musicStatus.textContent = '播放中';
            musicToggle.classList.add('music-active');
        } else {
            musicIcon.textContent = '🔇';
            musicStatus.textContent = '已静音';
            musicToggle.classList.remove('music-active');
        }
    }
    
    createClickRipple(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const ripple = document.createElement('div');
        ripple.style.position = 'absolute';
        ripple.style.left = (rect.left + x) + 'px';
        ripple.style.top = (rect.top + y) + 'px';
        ripple.style.width = '0px';
        ripple.style.height = '0px';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.5)';
        ripple.style.transform = 'translate(-50%, -50%)';
        ripple.style.pointerEvents = 'none';
        ripple.style.zIndex = '1000';
        
        document.body.appendChild(ripple);
        
        // Animate ripple
        gsap.to(ripple, {
            width: '100px',
            height: '100px',
            opacity: 0,
            duration: 0.6,
            ease: "power2.out",
            onComplete: () => {
                document.body.removeChild(ripple);
            }
        });
    }
    
    animateHeartExplosion() {
        // Canvas explosion animation
        this.canvas.classList.add('heart-explode');
        
        // Create explosion particles
        this.createExplosionParticles();
        
        // Remove class after animation
        setTimeout(() => {
            this.canvas.classList.remove('heart-explode');
        }, 600);
    }
    
    createExplosionParticles() {
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        for (let i = 0; i < 12; i++) {
            const particle = document.createElement('div');
            particle.innerHTML = '❤️';
            particle.style.position = 'fixed';
            particle.style.left = centerX + 'px';
            particle.style.top = centerY + 'px';
            particle.style.fontSize = '1.5rem';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '1000';
            
            document.body.appendChild(particle);
            
            // Random direction and distance
            const angle = (i / 12) * Math.PI * 2;
            const distance = 80 + Math.random() * 40;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance;
            
            // Animate particle
            gsap.to(particle, {
                x: endX - centerX,
                y: endY - centerY,
                opacity: 0,
                scale: 0.5,
                duration: 1,
                ease: "power2.out",
                onComplete: () => {
                    document.body.removeChild(particle);
                }
            });
        }
    }
    
    createHeartParticles() {
        const container = document.getElementById('particles-container');
        
        for (let i = 0; i < 6; i++) {
            const particle = document.createElement('div');
            particle.innerHTML = '💕';
            particle.className = 'heart-particle';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';
            
            container.appendChild(particle);
            
            // Animate particle floating up
            gsap.to(particle, {
                y: -200,
                x: (Math.random() - 0.5) * 100,
                opacity: 0,
                duration: 2,
                ease: "power2.out",
                onComplete: () => {
                    container.removeChild(particle);
                }
            });
        }
    }
    
    showNextMessage() {
        const messageText = document.getElementById('message-text');
        const progressText = document.getElementById('progress-text');
        const heartPrompt = document.getElementById('heart-prompt');
        
        // Update progress
        this.currentMessage = (this.currentMessage + 1) % this.messages.length;
        const displayIndex = this.currentMessage === 0 ? this.messages.length : this.currentMessage;
        progressText.textContent = `${displayIndex}/6`;
        
        // Update message with fade animation
        gsap.to(messageText, {
            opacity: 0,
            y: -10,
            duration: 0.3,
            ease: "power2.out",
            onComplete: () => {
                messageText.textContent = this.messages[this.currentMessage];
                gsap.to(messageText, {
                    opacity: 1,
                    y: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
        });
        
        // Update heart prompt
        if (this.currentMessage === 0) {
            heartPrompt.querySelector('span').textContent = '点击❤️重新开始';
        } else {
            heartPrompt.querySelector('span').textContent = `继续点击❤️ (${6 - displayIndex}次)`;
        }
        
        // Pulse animation for prompt
        gsap.to(heartPrompt, {
            scale: 1.1,
            duration: 0.2,
            yoyo: true,
            repeat: 1,
            ease: "power2.inOut"
        });
    }
    
    toggleMusic() {
        const music = document.getElementById('bg-music');
        const musicIcon = document.getElementById('music-icon');
        const musicStatus = document.getElementById('music-status');
        const musicToggle = document.getElementById('music-toggle');
        
        if (this.musicEnabled) {
            music.pause();
            musicIcon.textContent = '🎵';
            musicStatus.textContent = '音乐';
            musicToggle.classList.remove('music-active');
            this.musicEnabled = false;
        } else {
            // 简化播放逻辑
            music.volume = 0.5;
            const playPromise = music.play();
            
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    musicIcon.textContent = '🎶';
                    musicStatus.textContent = '播放中';
                    musicToggle.classList.add('music-active');
                    this.musicEnabled = true;
                    console.log('Music started successfully');
                }).catch(e => {
                    console.error('Music play failed:', e);
                    if (e.name === 'AbortError' || e.name === 'NotSupportedError') {
                        this.showToast('浏览器不支持此音频格式');
                    } else if (e.name === 'NotAllowedError') {
                        this.showToast('请允许浏览器播放音乐');
                    } else if (e.name === 'NetworkError') {
                        this.showToast('音乐文件加载失败 (404)');
                    } else {
                        this.showToast('音乐播放失败，错误代码: ' + e.name);
                    }
                });
            }
        }
        
        localStorage.setItem('heartapp_music_enabled', this.musicEnabled);
    }
    
    shareApp() {
        const shareBtn = document.getElementById('share-btn');
        shareBtn.classList.add('share-animation');
        
        setTimeout(() => {
            shareBtn.classList.remove('share-animation');
        }, 600);
        
        if (navigator.share) {
            navigator.share({
                title: '心动告白',
                text: '用这个浪漫的告白应用表达你的心意吧！',
                url: window.location.href
            }).catch(err => console.log('Share failed:', err));
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showToast('链接已复制到剪贴板');
            }).catch(() => {
                this.showToast('分享失败，请手动复制链接');
            });
        }
    }
    
    installApp() {
        if (!this.deferredPrompt) return;
        
        // 触发安装提示
        this.deferredPrompt.prompt();
        
        // 处理用户选择结果
        this.deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('用户接受了安装');
                this.showToast('应用已添加到桌面');
            } else {
                console.log('用户拒绝了安装');
                this.showToast('安装已取消');
            }
            
            // 重置deferredPrompt变量
            this.deferredPrompt = null;
            // 隐藏安装按钮
            document.getElementById('install-btn').style.display = 'none';
        });
    }
    
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    showInstallGuide() {
        // 检查是否已经安装
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        if (isStandalone) return;
        
        // 检查是否已经显示过提示
        const shownBefore = localStorage.getItem('installGuideShown');
        if (shownBefore) return;
        
        // 10秒后显示安装提示
        setTimeout(() => {
            this.showToast('点击右上角菜单，选择"添加到主屏幕"安装应用');
            localStorage.setItem('installGuideShown', 'true');
        }, 10000);
    }
    
    takeScreenshot() {
        // Create flash effect
        const flash = document.createElement('div');
        flash.className = 'screenshot-flash';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            document.body.removeChild(flash);
        }, 300);
        
        // Use html2canvas if available, otherwise show message
        if (typeof html2canvas !== 'undefined') {
            html2canvas(document.body).then(canvas => {
                const link = document.createElement('a');
                link.download = 'heart-confession.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        } else {
            this.showToast('请使用浏览器的截图功能');
        }
    }
    
    openSettings() {
        const modal = document.getElementById('settings-modal');
        modal.classList.add('active');
        
        // Load current messages into inputs
        for (let i = 0; i < 6; i++) {
            document.getElementById(`message-${i + 1}`).value = this.messages[i];
        }
        
        // Animate modal appearance
        gsap.fromTo(modal.querySelector('.modal-content'), {
            scale: 0.8,
            opacity: 0
        }, {
            scale: 1,
            opacity: 1,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
    
    closeSettings() {
        const modal = document.getElementById('settings-modal');
        const content = modal.querySelector('.modal-content');
        
        gsap.to(content, {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            ease: "power2.inOut",
            onComplete: () => {
                modal.classList.remove('active');
            }
        });
    }
    
    saveCustomMessages() {
        const newMessages = [];
        let hasCustom = false;
        
        for (let i = 0; i < 6; i++) {
            const input = document.getElementById(`message-${i + 1}`);
            const message = input.value.trim();
            
            if (message) {
                newMessages.push(message);
                hasCustom = true;
            } else {
                newMessages.push(this.defaultMessages[i]);
            }
        }
        
        if (hasCustom) {
            this.messages = newMessages;
            localStorage.setItem('heartapp_custom_messages', JSON.stringify(this.messages));
            this.showToast('自定义消息已保存');
        } else {
            this.showToast('请至少输入一条消息');
            return;
        }
        
        this.closeSettings();
    }
    
    resetToDefault() {
        this.messages = [...this.defaultMessages];
        localStorage.removeItem('heartapp_custom_messages');
        
        // Update input fields
        for (let i = 0; i < 6; i++) {
            document.getElementById(`message-${i + 1}`).value = this.messages[i];
        }
        
        this.showToast('已恢复默认消息');
    }
    
    createBackgroundParticles() {
        const container = document.getElementById('particles-container');
        
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = Math.random() * window.innerHeight + 'px';
            particle.style.animationDelay = Math.random() * 3 + 's';
            particle.style.animationDuration = (3 + Math.random() * 2) + 's';
            
            container.appendChild(particle);
        }
    }
    
    startBackgroundAnimation() {
        // Animate background gradient
        gsap.to(document.body, {
            duration: 10,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut",
            background: "linear-gradient(135deg, #c44569 0%, #ff6b9d 50%, #ff8fab 100%)"
        });
    }
    
    loadUserPreferences() {
        // Load music preference
        const musicEnabled = localStorage.getItem('heartapp_music_enabled') === 'true';
        if (musicEnabled) {
            this.musicEnabled = true;
            document.getElementById('music-toggle').classList.add('music-active');
        }
        
        // Load custom messages
        const customMessages = localStorage.getItem('heartapp_custom_messages');
        if (customMessages) {
            try {
                this.messages = JSON.parse(customMessages);
            } catch (e) {
                console.error('Failed to load custom messages:', e);
            }
        }
    }
    
    handleResize() {
        this.setupCanvas();
        this.drawHeart();
    }
    
    showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.position = 'fixed';
        toast.style.bottom = '20px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.background = 'rgba(0, 0, 0, 0.8)';
        toast.style.color = 'white';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '20px';
        toast.style.fontSize = '0.9rem';
        toast.style.zIndex = '9999';
        toast.style.opacity = '0';
        
        document.body.appendChild(toast);
        
        gsap.to(toast, {
            opacity: 1,
            duration: 0.3,
            ease: "power2.out"
        });
        
        setTimeout(() => {
            gsap.to(toast, {
                opacity: 0,
                duration: 0.3,
                ease: "power2.out",
                onComplete: () => {
                    document.body.removeChild(toast);
                }
            });
        }, 2000);
    }
    
    setupPerformanceOptimizations() {
        // Optimize canvas rendering
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
        
        // Reduce particle count on lower-end devices
        this.detectDeviceCapabilities();
        
        // Debounce heart clicks
        this.debouncedHeartClick = this.debounce(this.handleHeartClick.bind(this), 100);
        
        // Optimize memory usage
        this.setupMemoryManagement();
        
        // Visibility API for performance (只影响动画，不影响音乐)
        this.setupVisibilityHandling();
    }
    
    detectDeviceCapabilities() {
        // Simple device capability detection
        const userAgent = navigator.userAgent.toLowerCase();
        const isOldDevice = /android\s[1-4]|iphone\sos\s[1-9]|windows\sphone/i.test(userAgent);
        const hasLowRAM = navigator.deviceMemory && navigator.deviceMemory < 4;
        const hasSlowConnection = navigator.connection && navigator.connection.effectiveType === 'slow-2g';
        
        this.isLowPerformanceDevice = isOldDevice || hasLowRAM || hasSlowConnection;
        
        if (this.isLowPerformanceDevice) {
            // Reduce particle count
            this.maxParticles = 5;
            this.maxExplosionParticles = 6;
            
            // Disable some animations
            this.useSimpleAnimations = true;
            
            console.log('Low performance device detected, optimizing...');
        } else {
            this.maxParticles = 20;
            this.maxExplosionParticles = 12;
            this.useSimpleAnimations = false;
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    setupMemoryManagement() {
        // Clean up particles periodically
        setInterval(() => {
            this.cleanupParticles();
        }, 10000);
        
        // Monitor memory usage if available
        if (window.performance && window.performance.memory) {
            this.monitorMemoryUsage();
        }
    }
    
    cleanupParticles() {
        const container = document.getElementById('particles-container');
        const particles = container.querySelectorAll('.particle, .heart-particle');
        
        // Remove particles that have finished animating
        particles.forEach(particle => {
            if (particle.style.opacity === '0' || particle.style.opacity === '') {
                container.removeChild(particle);
            }
        });
    }
    
    monitorMemoryUsage() {
        const memory = window.performance.memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        
        // If memory usage is high, reduce particle effects
        if (usedMB > 50 && totalMB > 100) {
            this.maxParticles = Math.max(5, this.maxParticles - 5);
            this.maxExplosionParticles = Math.max(3, this.maxExplosionParticles - 3);
            console.log('High memory usage detected, reducing particle count');
        }
    }
    
    setupVisibilityHandling() {
        // Pause animations when page is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
        
        // Pause on window blur
        window.addEventListener('blur', () => this.pauseAnimations());
        window.addEventListener('focus', () => this.resumeAnimations());
    }
    
    pauseAnimations() {
        // 注释掉音乐暂停逻辑，避免中断播放
        /*
        const music = document.getElementById('bg-music');
        if (this.musicEnabled && !music.paused) {
            music.pause();
            this.musicWasPaused = true;
        }
        */
        
        // Pause GSAP animations
        gsap.globalTimeline.pause();
        
        // Clear background particle animation
        this.backgroundAnimationPaused = true;
    }
    
    resumeAnimations() {
        // 注释掉音乐恢复逻辑
        /*
        const music = document.getElementById('bg-music');
        if (this.musicEnabled && this.musicWasPaused) {
            music.play().catch(e => console.log('Music resume failed:', e));
            this.musicWasPaused = false;
        }
        */
        
        // Resume GSAP animations
        gsap.globalTimeline.resume();
        
        // Resume background particle animation
        this.backgroundAnimationPaused = false;
    }
    
    optimizeCanvas() {
        // Use requestAnimationFrame for smooth animations
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        
        const render = () => {
            if (!this.backgroundAnimationPaused) {
                this.drawHeart();
            }
            this.animationFrameId = requestAnimationFrame(render);
        };
        
        render();
    }
    
    // Override particle creation for performance
    createBackgroundParticles() {
        const container = document.getElementById('particles-container');
        
        for (let i = 0; i < this.maxParticles; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = Math.random() * window.innerHeight + 'px';
            particle.style.animationDelay = Math.random() * 3 + 's';
            particle.style.animationDuration = (3 + Math.random() * 2) + 's';
            
            container.appendChild(particle);
        }
    }
    
    // Override explosion particles for performance
    createExplosionParticles() {
        if (this.useSimpleAnimations) {
            // Simple explosion for low-end devices
            this.canvas.style.filter = 'brightness(1.2) saturate(1.2)';
            setTimeout(() => {
                this.canvas.style.filter = 'none';
            }, 200);
            return;
        }
        
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        for (let i = 0; i < this.maxExplosionParticles; i++) {
            const particle = document.createElement('div');
            particle.innerHTML = '❤️';
            particle.style.position = 'fixed';
            particle.style.left = centerX + 'px';
            particle.style.top = centerY + 'px';
            particle.style.fontSize = '1.5rem';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '1000';
            
            document.body.appendChild(particle);
            
            // Random direction and distance
            const angle = (i / this.maxExplosionParticles) * Math.PI * 2;
            const distance = 80 + Math.random() * 40;
            const endX = centerX + Math.cos(angle) * distance;
            const endY = centerY + Math.sin(angle) * distance;
            
            // Animate particle
            gsap.to(particle, {
                x: endX - centerX,
                y: endY - centerY,
                opacity: 0,
                scale: 0.5,
                duration: 1,
                ease: "power2.out",
                onComplete: () => {
                    document.body.removeChild(particle);
                }
            });
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HeartConfessionApp();
});

// Register service worker for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('sw.js')
            .then(registration => {
                console.log('[SERVICE WORKER] 注册成功', registration.scope);
            })
            .catch(error => {
                console.error('[SERVICE WORKER] 注册失败', error);
            });
    });
}
