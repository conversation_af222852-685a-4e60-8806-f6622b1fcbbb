<!DOCTYPE html>
<html>
<head>
    <title>创建图标</title>
</head>
<body>
    <canvas id="icon-canvas" width="192" height="192"></canvas>
    <br>
    <button onclick="downloadIcon()">下载图标</button>
    
    <script>
        const canvas = document.getElementById('icon-canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制背景
        const gradient = ctx.createLinearGradient(0, 0, 192, 192);
        gradient.addColorStop(0, '#ff6b9d');
        gradient.addColorStop(1, '#ffa726');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 192, 192);
        
        // 绘制心形
        const centerX = 96;
        const centerY = 96;
        const scale = 1.5;
        
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 40 * scale);
        
        // 左侧曲线
        ctx.bezierCurveTo(
            centerX - 50 * scale, centerY - 80 * scale,
            centerX - 90 * scale, centerY - 40 * scale,
            centerX - 50 * scale, centerY - 10 * scale
        );
        
        // 左下曲线
        ctx.bezierCurveTo(
            centerX - 30 * scale, centerY + 10 * scale,
            centerX - 20 * scale, centerY + 30 * scale,
            centerX, centerY + 50 * scale
        );
        
        // 右下曲线
        ctx.bezierCurveTo(
            centerX + 20 * scale, centerY + 30 * scale,
            centerX + 30 * scale, centerY + 10 * scale,
            centerX + 50 * scale, centerY - 10 * scale
        );
        
        // 右侧曲线
        ctx.bezierCurveTo(
            centerX + 90 * scale, centerY - 40 * scale,
            centerX + 50 * scale, centerY - 80 * scale,
            centerX, centerY - 40 * scale
        );
        
        ctx.closePath();
        ctx.fillStyle = 'white';
        ctx.fill();
        
        // 添加阴影
        ctx.shadowColor = 'rgba(0,0,0,0.3)';
        ctx.shadowBlur = 10;
        ctx.fill();
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon-192x192.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
